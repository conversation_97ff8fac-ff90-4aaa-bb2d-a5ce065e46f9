'use client';

import Link from 'next/link';

const HeroSection = () => {
  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
    >
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1540979388789-6cee28a1cdc9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`,
        }}
      />
      
      {/* Overlay */}
      <div className="absolute inset-0 hero-overlay" />

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Main Heading */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-serif font-bold text-white mb-6 leading-tight">
            Experience London's
            <span className="block text-accent-400">Elite Transportation</span>
          </h1>

          {/* Subheading */}
          <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
            From luxury helicopters soaring above the Thames to private jets connecting you globally,
            GoGeo Travels London delivers unparalleled comfort and sophistication in every journey.
          </p>

          {/* Service Types */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto">
            {[
              { icon: '🚁', name: 'Helicopters', desc: 'Sky-high luxury' },
              { icon: '✈️', name: 'Private Jets', desc: 'Global connections' },
              { icon: '🚌', name: 'Executive Buses', desc: 'Group comfort' },
              { icon: '🚗', name: 'Private Cars', desc: 'Personal elegance' },
            ].map((service, index) => (
              <div
                key={index}
                className="glass-effect rounded-xl p-4 text-center hover:bg-white/20 transition-all duration-300 cursor-pointer"
              >
                <div className="text-3xl mb-2">{service.icon}</div>
                <h3 className="text-white font-semibold text-sm md:text-base">
                  {service.name}
                </h3>
                <p className="text-white/70 text-xs md:text-sm">
                  {service.desc}
                </p>
              </div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link
              href="#booking"
              className="bg-accent-gradient text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-2xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
            >
              Book Your Journey
            </Link>
            <Link
              href="#fleet"
              className="glass-effect text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/20 transition-all duration-300 w-full sm:w-auto"
            >
              Explore Fleet
            </Link>
          </div>

          {/* Trust Indicators */}
          <div className="mt-16 pt-8 border-t border-white/20">
            <p className="text-white/70 text-sm mb-4">Trusted by London's Elite</p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              <div className="text-white text-sm">★★★★★ 5.0 Rating</div>
              <div className="text-white text-sm">500+ Happy Clients</div>
              <div className="text-white text-sm">24/7 Concierge</div>
              <div className="text-white text-sm">Fully Licensed & Insured</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <Link href="#fleet">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </Link>
      </div>
    </section>
  );
};

export default HeroSection;
