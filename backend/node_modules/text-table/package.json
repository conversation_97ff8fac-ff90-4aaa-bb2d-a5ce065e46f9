{"name": "text-table", "version": "0.2.0", "description": "borderless text tables with alignment", "main": "index.js", "devDependencies": {"tap": "~0.4.0", "tape": "~1.0.2", "cli-color": "~0.2.3"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/20..latest", "firefox/10..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "repository": {"type": "git", "url": "git://github.com/substack/text-table.git"}, "homepage": "https://github.com/substack/text-table", "keywords": ["text", "table", "align", "ascii", "rows", "tabular"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT"}